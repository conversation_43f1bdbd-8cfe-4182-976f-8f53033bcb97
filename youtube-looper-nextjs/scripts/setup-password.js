#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to set up password protection in Firebase
 * Usage: node scripts/setup-password.js "your_password_here"
 */

const { initializeApp, getApps } = require('firebase-admin/app');
const { getFirestore } = require('firebase-admin/firestore');
const bcrypt = require('bcryptjs');
const readline = require('readline');

// Get Firebase project ID from environment variables
const projectId = process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID;

async function setupPassword() {
  try {
    // Check if Firebase project ID is available
    if (!projectId) {
      console.error('❌ Firebase project ID not found.');
      console.error('Make sure NEXT_PUBLIC_FIREBASE_PROJECT_ID is set in your .env.local file.');
      process.exit(1);
    }

    // Get password from command line argument or prompt
    let password = process.argv[2];
    
    if (!password) {
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });

      password = await new Promise((resolve) => {
        rl.question('Enter the password for app access: ', (answer) => {
          rl.close();
          resolve(answer);
        });
      });
    }

    if (!password || password.trim() === '') {
      console.error('❌ Password cannot be empty.');
      process.exit(1);
    }

    // Initialize Firebase Admin
    let app;
    if (getApps().length === 0) {
      app = initializeApp({
        projectId: projectId,
      });
    } else {
      app = getApps()[0];
    }

    const db = getFirestore(app);

    // Hash the password before storing
    console.log('🔐 Hashing password...');
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password.trim(), saltRounds);

    // Set the hashed password in Firestore using Admin SDK
    const configRef = db.collection('app_config').doc('access_control');
    await configRef.set({
      password: hashedPassword,
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString()
    });

    console.log('✅ Password protection has been set up successfully!');
    console.log('🔒 Your app is now password protected.');
    console.log('📝 Users will need to enter this password to access the app.');
    console.log('');
    console.log('To update the password, run this script again with a new password.');
    
  } catch (error) {
    console.error('❌ Error setting up password:', error.message);
    process.exit(1);
  }
}

// Run the setup
setupPassword();
