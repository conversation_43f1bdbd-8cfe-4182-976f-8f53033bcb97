rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Personal queues - users can only access their own queues
    match /personal_queues/{queueId} {
      allow read, write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
    }

    // Unified queues collection - handles both public and private queues
    match /queues/{queueId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
    }

    // User profiles - users can only access their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // App configuration - secure access only for server-side verification
    // Passwords are now hashed and should not be accessible from client-side
    match /app_config/{configId} {
      allow read: if false; // No client-side access - only server-side API routes
      allow write: if false; // Prevent client-side writes
    }
    
    // Queue statistics - read-only for authenticated users
    match /stats/{document} {
      allow read: if request.auth != null;
      allow write: if false; // Only server-side functions can write stats
    }
  }
}
