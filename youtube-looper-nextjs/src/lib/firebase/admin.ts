import { initializeApp, getApps, cert, App } from 'firebase-admin/app'
import { getFirestore, Firestore } from 'firebase-admin/firestore'

let adminApp: App | null = null
let adminDb: Firestore | null = null

export function getFirebaseAdmin(): { app: App; db: Firestore } | null {
  try {
    // Check if admin app is already initialized
    if (adminApp && adminDb) {
      return { app: adminApp, db: adminDb }
    }

    // Check if any admin apps exist
    const existingApps = getApps()
    if (existingApps.length > 0) {
      adminApp = existingApps[0]
      adminDb = getFirestore(adminApp)
      return { app: adminApp, db: adminDb }
    }

    // Initialize Firebase Admin with project ID only (for server-side access)
    // This works when running on Firebase hosting or with proper service account
    const projectId = process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID
    
    if (!projectId) {
      console.error('❌ Firebase project ID not found in environment variables')
      return null
    }

    // Initialize with minimal config for server-side access
    adminApp = initializeApp({
      projectId: projectId,
    })

    adminDb = getFirestore(adminApp)
    
    console.log('✅ Firebase Admin initialized')
    return { app: adminApp, db: adminDb }
    
  } catch (error) {
    console.error('❌ Error initializing Firebase Admin:', error)
    return null
  }
}
