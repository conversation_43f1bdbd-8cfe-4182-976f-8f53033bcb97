import { NextRequest, NextResponse } from 'next/server'
import { initializeApp, getApps } from 'firebase/app'
import { getFirestore, doc, getDoc } from 'firebase/firestore'
import bcrypt from 'bcryptjs'

export async function POST(request: NextRequest) {
  try {
    console.log('🔐 Password verification API called')
    const { password } = await request.json()

    if (!password || typeof password !== 'string') {
      console.log('❌ Invalid password provided')
      return NextResponse.json(
        { error: 'Password is required' },
        { status: 400 }
      )
    }

    // Use Firebase Admin SDK for secure server-side access
    const firebase = getFirebaseAdmin()

    if (!firebase) {
      console.error('❌ Failed to initialize Firebase Admin')
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    const { db } = firebase

    // Get password from Firebase app_config collection using Admin SDK
    const configRef = db.collection('app_config').doc('access_control')
    const configSnap = await configRef.get()

    if (!configSnap.exists) {
      console.log('⚠️ No password configured in Firebase. Allowing access.')
      return NextResponse.json({ valid: true })
    }

    const configData = configSnap.data()
    const storedPassword = configData?.password

    if (!storedPassword) {
      console.log('⚠️ No password field found in Firebase. Allowing access.')
      return NextResponse.json({ valid: true })
    }

    // Verify password using bcrypt
    const isValid = await bcrypt.compare(password, storedPassword)
    console.log(`🔐 Password verification: ${isValid ? 'VALID' : 'INVALID'}`)

    return NextResponse.json({ valid: isValid })

  } catch (error) {
    console.error('❌ Error verifying password:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
