import { NextRequest, NextResponse } from 'next/server'
import { initializeApp, getApps } from 'firebase/app'
import { getFirestore, doc, getDoc } from 'firebase/firestore'
import bcrypt from 'bcryptjs'

export async function POST(request: NextRequest) {
  try {
    console.log('🔐 Password verification API called')
    const { password } = await request.json()

    if (!password || typeof password !== 'string') {
      console.log('❌ Invalid password provided')
      return NextResponse.json(
        { error: 'Password is required' },
        { status: 400 }
      )
    }

    // Use Firebase client SDK (works with current hosting setup)
    const firebaseConfig = {
      apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
      authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
      messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
      appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
    }

    // Initialize Firebase
    let app
    if (getApps().length === 0) {
      app = initializeApp(firebaseConfig)
    } else {
      app = getApps()[0]
    }

    const db = getFirestore(app)

    // Get password from Firebase app_config collection
    const configRef = doc(db, 'app_config', 'access_control')
    const configSnap = await getDoc(configRef)

    if (!configSnap.exists()) {
      console.log('⚠️ No password configured in Firebase. Allowing access.')
      return NextResponse.json({ valid: true })
    }

    const configData = configSnap.data()
    const storedPassword = configData?.password

    if (!storedPassword) {
      console.log('⚠️ No password field found in Firebase. Allowing access.')
      return NextResponse.json({ valid: true })
    }

    // Verify password using bcrypt
    const isValid = await bcrypt.compare(password, storedPassword)
    console.log(`🔐 Password verification: ${isValid ? 'VALID' : 'INVALID'}`)

    return NextResponse.json({ valid: isValid })

  } catch (error) {
    console.error('❌ Error verifying password:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
